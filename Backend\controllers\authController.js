import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import validator from 'validator';
import userModel from '../models/userModel.js';

// Create JWT token
const createToken = (id) => {
    return jwt.sign({ id }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

// Register user
const register = async (req, res) => {
    try {
        const { firstName, lastName, email, password } = req.body;

        // Validation
        if (!firstName || !lastName || !email || !password) {
            return res.json({ success: false, message: "All fields are required" });
        }

        // Validate email format
        if (!validator.isEmail(email)) {
            return res.json({ success: false, message: "Please enter a valid email" });
        }

        // Validate password strength
        if (password.length < 8) {
            return res.json({ success: false, message: "Password must be at least 8 characters long" });
        }

        // Check if user already exists
        const existingUser = await userModel.findOne({ email });
        if (existingUser) {
            return res.json({ success: false, message: "User already exists with this email" });
        }

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Create new user
        const newUser = new userModel({
            name: `${firstName} ${lastName}`,
            email,
            password: hashedPassword
        });

        const user = await newUser.save();

        // Create token
        const token = createToken(user._id);

        res.json({
            success: true,
            message: "Account created successfully",
            token,
            user: {
                id: user._id,
                name: user.name,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.json({ success: false, message: "Server error. Please try again." });
    }
};

// Login user
const login = async (req, res) => {
    try {
        const { email, password } = req.body;

        // Validation
        if (!email || !password) {
            return res.json({ success: false, message: "Email and password are required" });
        }

        // Find user
        const user = await userModel.findOne({ email });
        if (!user) {
            return res.json({ success: false, message: "User not found with this email" });
        }

        // Check password
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return res.json({ success: false, message: "Invalid password" });
        }

        // Create token
        const token = createToken(user._id);

        res.json({
            success: true,
            message: "Login successful",
            token,
            user: {
                id: user._id,
                name: user.name,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.json({ success: false, message: "Server error. Please try again." });
    }
};

// Google OAuth login
const googleLogin = async (req, res) => {
    try {
        const { googleToken, email, name } = req.body;

        // Validate Google token (implement Google token verification)
        // For now, we'll create/login user based on email

        let user = await userModel.findOne({ email });

        if (!user) {
            // Create new user from Google data
            user = new userModel({
                name,
                email,
                password: null, // No password for Google users
                googleId: googleToken // Store Google ID
            });
            await user.save();
        }

        // Create JWT token
        const token = createToken(user._id);

        res.json({
            success: true,
            message: "Google login successful",
            token,
            user: {
                id: user._id,
                name: user.name,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Google login error:', error);
        res.json({ success: false, message: "Google authentication failed" });
    }
};

// Get user profile
const getMe = async (req, res) => {
    try {
        const user = await userModel.findById(req.user.id).select('-password');
        
        if (!user) {
            return res.json({ success: false, message: "User not found" });
        }

        res.json({
            success: true,
            user: {
                id: user._id,
                name: user.name,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Get profile error:', error);
        res.json({ success: false, message: "Server error" });
    }
};

// Forgot password
const forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;

        // Validate email
        if (!email || !validator.isEmail(email)) {
            return res.json({ success: false, message: "Please enter a valid email" });
        }

        // Check if user exists
        const user = await userModel.findOne({ email });
        if (!user) {
            return res.json({ success: false, message: "No account found with this email" });
        }

        // Generate reset token (implement actual email sending)
        const resetToken = jwt.sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });

        // In a real application, you would:
        // 1. Save the reset token to the database
        // 2. Send an email with the reset link
        // 3. The reset link would contain the token

        // For now, we'll just return success
        res.json({
            success: true,
            message: "Password reset link sent to your email",
            // In development, you might want to return the token for testing
            // resetToken: resetToken
        });

    } catch (error) {
        console.error('Forgot password error:', error);
        res.json({ success: false, message: "Server error. Please try again." });
    }
};

// Reset password
const resetPassword = async (req, res) => {
    try {
        const { token, newPassword } = req.body;

        // Validate inputs
        if (!token || !newPassword) {
            return res.json({ success: false, message: "Token and new password are required" });
        }

        if (newPassword.length < 8) {
            return res.json({ success: false, message: "Password must be at least 8 characters long" });
        }

        // Verify reset token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await userModel.findById(decoded.id);

        if (!user) {
            return res.json({ success: false, message: "Invalid or expired reset token" });
        }

        // Hash new password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(newPassword, salt);

        // Update user password
        await userModel.findByIdAndUpdate(user._id, { password: hashedPassword });

        res.json({
            success: true,
            message: "Password reset successful"
        });

    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            return res.json({ success: false, message: "Invalid or expired reset token" });
        }
        
        console.error('Reset password error:', error);
        res.json({ success: false, message: "Server error. Please try again." });
    }
};

export { register, login, googleLogin, getMe, forgotPassword, resetPassword };

@charset "UTF-8";
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
  scroll-behavior: smooth;
}

:root {
  --primary-color: #ffffff;
  --secondary-color: #000;
  --accent-color: #ff6464;
  --lightgreen-color:#25ff259f;
  --green-color:#25ff25e0;
  --text-color: #333;
  --light-gray: #f8f8f8;
  --medium-gray: #e0e0e0;
  --dark-gray: #666;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Common Styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

section {
  padding: 60px 0;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 20px;
  font-weight: 600;
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
}

h2 {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 40px;
  position: relative;
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
}

h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--accent-color);
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  transition: var(--transition);
}

main {
  position: relative;
  width: 100%;
  /* Women's Collection Section */
  /* Features Section */
  /* Gallery and Price Section */
  /* Reviews Section */
}
main header {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  /* Hero Section */
}
main header #cursor {
  height: 2.8%;
  width: 1.5%;
  background-color: #0a0a0a;
  border-radius: 50%;
  position: fixed;
  z-index: 9;
  box-shadow: 0px 0px 10px 1px #000000;
  display: none;
}
main header nav {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: transparent;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  opacity: 1;
  position: fixed;
  top: 0;
  left: 0;
  transition: top 0.3s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  z-index: 20;
}
main header nav #logo {
  height: 7%;
  width: 4%;
  margin-left: 1rem;
  margin-right: 10rem;
}
main header nav #logo img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
main header nav #nav-middle {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  justify-content: center;
}
main header nav #nav-middle a {
  text-decoration: none;
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
  color: var(--secondary-color);
  font-weight: 500;
  position: relative;
  padding: 5px 0;
}
main header nav #nav-middle a::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 0;
  transition: var(--transition);
}
main header nav #nav-middle a:hover::after {
  width: 0;
}
main header nav #nav-middle a:hover {
  color: rgba(17, 17, 17, 0.5333333333);
}
main header nav #nav-last {
  display: flex;
  gap: 1.4rem;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}
main header nav #nav-last #search-bar {
  display: flex;
  border-radius: 50px;
  border: 2px solid #111;
  width: 100%;
}
main header nav #nav-last #search-bar i {
  font-size: 1.3em;
}
main header nav #nav-last #search-bar .search-icon {
  margin-left: 0.2rem;
  margin-top: 0.3rem;
  border-radius: 50% 0 0 50%;
  background-color: transparent;
}
main header nav #nav-last #search-bar #nav-search {
  border: none;
  padding: 0.2rem;
  width: 10vw;
  border-radius: 0 50px 50px 0;
  font-size: 1rem;
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
  outline: none;
  background-color: transparent;
  color: black;
}
main header nav #nav-last #search-bar #nav-search:active {
  background-color: transparent;
}
main header nav #nav-last #search-bar #nav-search::-webkit-search-cancel-button {
  cursor: pointer;
}
main header nav #nav-last .cart-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
main header nav #nav-last .cart-icon-container .cart {
  font-size: 1.5rem;
  padding: 5px;
  border-radius: 50%;
  transition: var(--transition);
}
main header nav #nav-last .cart-icon-container .cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--accent-color);
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}
main header nav #nav-last .cart-icon-container.animate .cart-count {
  animation: cartBounce 0.5s ease;
}
main header nav #nav-last .cart-icon-container:hover,
main header nav #nav-last .cart-icon-container:active {
  cursor: pointer;
}
main header nav #nav-last .cart-icon-container:hover .cart,
main header nav #nav-last .cart-icon-container:active .cart {
  color: rgba(17, 17, 17, 0.5333333333);
  transform: scale(1.1);
}
main header nav #nav-last .user {
  font-size: 1.7rem;
  transition: var(--transition);
}
main header nav #nav-last .user:hover {
  cursor: pointer;
  color: rgba(17, 17, 17, 0.5333333333);
  transform: scale(1.1);
}
main header #hero {
  height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #fff0f5 0%, #ffd1dc 100%);
  padding-top: 80px;
  position: relative;
  overflow: hidden;
}
main header #hero::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: url("/assets/images/pattern-dots.png");
  opacity: 0.05;
  z-index: 0;
}
main header #hero::after {
  content: "";
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 77, 77, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}
main header #hero .hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
  position: relative;
  z-index: 1;
}
main header #hero .hero-content .hero-text {
  flex: 1;
  padding-right: 40px;
}
main header #hero .hero-content .hero-text h1 {
  font-size: 4rem;
  margin-bottom: 20px;
  line-height: 1.1;
  color: var(--secondary-color);
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
  font-weight: 700;
  letter-spacing: -0.5px;
  position: relative;
}
main header #hero .hero-content .hero-text h1::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100px;
  height: 4px;
  background-color: var(--accent-color);
}
main header #hero .hero-content .hero-text p {
  font-size: 1.3rem;
  color: var(--dark-gray);
  margin-bottom: 40px;
  line-height: 1.6;
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
  max-width: 90%;
}
main header #hero .hero-content .hero-text .buy-now-btn {
  background-color: var(--secondary-color);
  color: white;
  padding: 15px 35px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 30px;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  z-index: 1;
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
  letter-spacing: 0.5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
main header #hero .hero-content .hero-text .buy-now-btn:active {
  transform: scale(1);
}
main header #hero .hero-content .hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
main header #hero .hero-content .hero-image::before {
  content: "";
  position: absolute;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 77, 77, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 6s infinite alternate ease-in-out;
}
main header #hero .hero-content .hero-image::after {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 8s infinite alternate-reverse ease-in-out;
}
main header #hero .hero-content .hero-image img {
  max-width: 100%;
  height: auto;
  transform: rotate(-15deg);
  transition: var(--transition);
  filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.25));
  animation: float 6s infinite ease-in-out;
}
main header #hero .hero-content .hero-image img:hover {
  transform: rotate(-10deg) scale(1.05);
}
@keyframes float {
  0% {
    transform: rotate(-15deg) translateY(0px);
  }
  50% {
    transform: rotate(-12deg) translateY(-15px);
  }
  100% {
    transform: rotate(-15deg) translateY(0px);
  }
}
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}
main #womens-collection {
  padding: 80px 0;
  background-color: white;
}
main #womens-collection h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 50px;
  position: relative;
}
main #womens-collection h2::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--accent-color);
}
main #womens-collection .products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
@media (max-width: 992px) {
  main #womens-collection .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 576px) {
  main #womens-collection .products-grid {
    grid-template-columns: 1fr;
  }
}
main #womens-collection .products-grid .product-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  user-select: none;
}
main #womens-collection .products-grid .product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}
main #womens-collection .products-grid .product-card:hover .product-image img {
  transform: scale(1.05);
}
main #womens-collection .products-grid .product-card .product-image {
  width: 100%;
  height: 400px;
  overflow: hidden;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
}
main #womens-collection .products-grid .product-card .product-image img {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.5s ease;
}
main #womens-collection .products-grid .product-card .product-info {
  padding: 15px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
main #womens-collection .products-grid .product-card .product-info .product-name {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #000;
  font-family: "Helvetica Now Display", Arial, sans-serif;
}
main #womens-collection .products-grid .product-card .product-info .product-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}
main #womens-collection .products-grid .product-card .product-info .product-price .original-price {
  font-size: 0.9rem;
  color: #777;
  text-decoration: line-through;
}
main #womens-collection .products-grid .product-card .product-info .product-price .current-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #000;
}
main #womens-collection .products-grid .product-card .product-info .product-price .discount-badge {
  background-color: #ff5a5f;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}
main #womens-collection .products-grid .product-card .product-info .add-to-cart-btn {
  background-color: #000;
  color: white;
  padding: 8px 0;
  font-size: 0.85rem;
  font-weight: 600;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: auto;
  font-family: "Helvetica Now Display", Arial, sans-serif;
}
main #womens-collection .products-grid .product-card .product-info .add-to-cart-btn:hover {
  background-color: #333;
}
main #womens-collection .products-grid .product-card .product-info .add-to-cart-btn:active {
  transform: scale(0.98);
}
main #features {
  background-color: var(--light-gray);
  padding: 80px 0;
}
main #features .features-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 30px;
}
main #features .features-container .feature {
  flex: 1;
  min-width: 250px;
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: var(--shadow);
  text-align: center;
  transition: var(--transition);
}
main #features .features-container .feature:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}
main #features .features-container .feature i {
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 20px;
}
main #features .features-container .feature h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: var(--secondary-color);
}
main #features .features-container .feature p {
  color: var(--dark-gray);
  line-height: 1.6;
}
main #gallery-price {
  padding: 80px 0;
  background-color: white;
}
main #gallery-price .gallery-price-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
main #gallery-price .gallery-container {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
}
main #gallery-price .gallery-container .gallery-left {
  flex: 1;
  min-width: 300px;
  display: flex;
  gap: 20px;
}
main #gallery-price .gallery-container .gallery-left .thumbnails {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
main #gallery-price .gallery-container .gallery-left .thumbnails .thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  -o-object-fit: cover;
     object-fit: cover;
  cursor: pointer;
  opacity: 0.7;
  transition: var(--transition);
  border: 2px solid transparent;
}
main #gallery-price .gallery-container .gallery-left .thumbnails .thumbnail:hover {
  opacity: 1;
}
main #gallery-price .gallery-container .gallery-left .thumbnails .thumbnail.active {
  opacity: 1;
  border-color: var(--accent-color);
}
main #gallery-price .gallery-container .gallery-left .main-image {
  flex: 1;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow);
  background-color: #F5F5F5;
}
main #gallery-price .gallery-container .gallery-left .main-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  transition: opacity 0.3s ease;
}
main #gallery-price .gallery-container .price-container {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}
main #gallery-price .gallery-container .price-container .price-info {
  background-color: var(--light-gray);
  padding: 30px;
  border-radius: 10px;
  box-shadow: var(--shadow);
}
main #gallery-price .gallery-container .price-container .price-info h2 {
  text-align: left;
  margin-bottom: 20px;
}
main #gallery-price .gallery-container .price-container .price-info h2::after {
  left: 0;
  transform: none;
}
main #gallery-price .gallery-container .price-container .price-info .product-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 15px;
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
}
main #gallery-price .gallery-container .price-container .price-info .price-display {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  transition: var(--transition);
}
main #gallery-price .gallery-container .price-container .price-info .price-display .original-price {
  font-size: 1.2rem;
  color: var(--dark-gray);
  text-decoration: line-through;
}
main #gallery-price .gallery-container .price-container .price-info .price-display .current-price {
  font-size: 2rem;
  font-weight: 700;
  color: var(--secondary-color);
}
main #gallery-price .gallery-container .price-container .price-info .price-display .discount-badge {
  background-color: var(--accent-color);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
}
main #gallery-price .gallery-container .price-container .price-info .limited-offer {
  color: var(--accent-color);
  font-weight: 600;
  margin-bottom: 30px;
}
main #gallery-price .gallery-container .price-container .price-info .product-description {
  margin-bottom: 30px;
}
main #gallery-price .gallery-container .price-container .price-info .product-description h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
}
main #gallery-price .gallery-container .price-container .price-info .product-description p {
  color: var(--dark-gray);
  line-height: 1.6;
}
main #gallery-price .gallery-container .price-container .price-info .add-to-cart-btn {
  background-color: var(--secondary-color);
  color: white;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 30px;
  width: 100%;
  transition: var(--transition);
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
}
main #gallery-price .gallery-container .price-container .price-info .add-to-cart-btn:hover {
  background-color: var(--accent-color);
  transform: translateY(-2px);
}
main #gallery-price .gallery-container .price-container .price-info .add-to-cart-btn:active {
  transform: translateY(0);
}
main #gallery-price .gallery-container .price-container .price-info .back-to-collection-btn {
  background-color: transparent;
  color: var(--secondary-color);
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  border: 2px solid var(--secondary-color);
  border-radius: 30px;
  width: 100%;
  margin-top: 15px;
  transition: var(--transition);
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
}
main #gallery-price .gallery-container .price-container .price-info .back-to-collection-btn:hover {
  background-color: var(--secondary-color);
  color: white;
}
main #gallery-price .gallery-container .price-container .size-selector {
  background-color: var(--light-gray);
  padding: 30px;
  border-radius: 10px;
  box-shadow: var(--shadow);
}
main #gallery-price .gallery-container .price-container .size-selector h3 {
  margin-bottom: 20px;
}
main #gallery-price .gallery-container .price-container .size-selector .size-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}
main #gallery-price .gallery-container .price-container .size-selector .size-options .size-btn {
  padding: 10px;
  background-color: white;
  border: 1px solid var(--medium-gray);
  border-radius: 5px;
  font-size: 0.9rem;
  transition: var(--transition);
}
main #gallery-price .gallery-container .price-container .size-selector .size-options .size-btn:hover {
  border-color: var(--secondary-color);
}
main #gallery-price .gallery-container .price-container .size-selector .size-options .size-btn.active {
  background-color: var(--secondary-color);
  color: white;
  border-color: var(--secondary-color);
}
main #reviews {
  background-color: var(--light-gray);
  padding: 80px 0;
}
main #reviews .reviews-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
main #reviews .reviews-container .review-stats {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}
main #reviews .reviews-container .review-stats .average-rating {
  text-align: center;
}
main #reviews .reviews-container .review-stats .average-rating .rating-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--secondary-color);
}
main #reviews .reviews-container .review-stats .average-rating .stars {
  margin: 10px 0;
  color: #FFD700;
  font-size: 1.5rem;
}
main #reviews .reviews-container .review-stats .average-rating .total-reviews {
  color: var(--dark-gray);
}
main #reviews .reviews-container .review-carousel {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: var(--shadow);
}
main #reviews .reviews-container .review-carousel .review-slide {
  display: none;
}
main #reviews .reviews-container .review-carousel .review-slide.active {
  display: block;
}
main #reviews .reviews-container .review-carousel .review-content {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
}
main #reviews .reviews-container .review-carousel .review-content .reviewer-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
main #reviews .reviews-container .review-carousel .review-content .reviewer-info .reviewer-name {
  font-weight: 600;
  color: var(--secondary-color);
}
main #reviews .reviews-container .review-carousel .review-content .reviewer-info .review-date {
  color: var(--dark-gray);
  font-size: 0.9rem;
}
main #reviews .reviews-container .review-carousel .review-content .stars {
  color: #FFD700;
  margin-bottom: 15px;
}
main #reviews .reviews-container .review-carousel .review-content .review-text {
  color: var(--dark-gray);
  line-height: 1.6;
  font-style: italic;
}
main #reviews .reviews-container .carousel-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 20px;
}
main #reviews .reviews-container .carousel-controls .prev-btn,
main #reviews .reviews-container .carousel-controls .next-btn {
  background-color: white;
  color: var(--secondary-color);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
}
main #reviews .reviews-container .carousel-controls .prev-btn:hover,
main #reviews .reviews-container .carousel-controls .next-btn:hover {
  background-color: var(--secondary-color);
  color: white;
}
main #reviews .reviews-container .carousel-controls .carousel-dots {
  display: flex;
  gap: 10px;
}
main #reviews .reviews-container .carousel-controls .carousel-dots .dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--medium-gray);
  cursor: pointer;
  transition: var(--transition);
}
main #reviews .reviews-container .carousel-controls .carousel-dots .dot.active {
  background-color: var(--secondary-color);
  transform: scale(1.2);
}

/* Footer */
footer {
  background-color: var(--secondary-color);
  color: white;
  padding: 60px 0 20px;
}
footer .footer-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 40px;
}
footer .footer-container .footer-column {
  flex: 1;
  min-width: 200px;
}
footer .footer-container .footer-column h3 {
  font-size: 1.3rem;
  margin-bottom: 20px;
  position: relative;
}
footer .footer-container .footer-column h3::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--accent-color);
}
footer .footer-container .footer-column ul {
  list-style: none;
}
footer .footer-container .footer-column ul li {
  margin-bottom: 10px;
}
footer .footer-container .footer-column ul li a {
  color: #ccc;
  text-decoration: none;
  transition: var(--transition);
}
footer .footer-container .footer-column ul li a:hover {
  color: white;
}
footer .footer-container .footer-column.social .social-icons {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}
footer .footer-container .footer-column.social .social-icons a {
  color: white;
  font-size: 1.5rem;
  transition: var(--transition);
}
footer .footer-container .footer-column.social .social-icons a:hover {
  color: var(--accent-color);
  transform: translateY(-3px);
}
footer .footer-container .footer-column.social .newsletter h4 {
  margin-bottom: 15px;
}
footer .footer-container .footer-column.social .newsletter .newsletter-form {
  display: flex;
}
footer .footer-container .footer-column.social .newsletter .newsletter-form input {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 4px 0 0 4px;
  outline: none;
}
footer .footer-container .footer-column.social .newsletter .newsletter-form button {
  background-color: var(--accent-color);
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  transition: var(--transition);
}
footer .footer-container .footer-column.social .newsletter .newsletter-form button:hover {
  background-color: #ff4f4f;
}
footer .footer-bottom {
  max-width: 1200px;
  margin: 40px auto 0;
  padding: 20px 20px 0;
  border-top: 1px solid #444;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}
footer .footer-bottom .copyright {
  color: #999;
  font-size: 0.9rem;
}
footer .footer-bottom .footer-links {
  display: flex;
  gap: 20px;
}
footer .footer-bottom .footer-links a {
  color: #999;
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition);
}
footer .footer-bottom .footer-links a:hover {
  color: white;
}

/* Cart Modal */
.cart-modal {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: white;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s ease;
  overflow-y: auto;
}
.cart-modal.open {
  right: 0;
}
.cart-modal .cart-modal-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.cart-modal .cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--medium-gray);
}
.cart-modal .cart-header h3 {
  font-size: 1.5rem;
  margin: 0;
}
.cart-modal .cart-header h3::before {
  content: "🛒";
  font-size: 1.2rem;
}
.cart-modal .cart-header .close-cart-btn {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: var(--dark-gray);
  transition: var(--transition);
  border-radius: 50%;
  padding: 2px 11px;
}
.cart-modal .cart-header .close-cart-btn:hover {
  background-color: rgba(0, 0, 0, 0.0470588235);
  color: var(--accent-color);
  rotate: 90deg;
}
.cart-modal .cart-items {
  flex: 1;
  overflow-y: auto;
}
.cart-modal .cart-items .empty-cart-message {
  text-align: center;
  padding: 50px 0;
  color: var(--dark-gray);
  font-size: 1.1rem;
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.cart-modal .cart-items .empty-cart-message p {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}
.cart-modal .cart-items .empty-cart-message .empty-cart-divider {
  width: 50px;
  height: 3px;
  background-color: var(--medium-gray);
  border-radius: 3px;
}
.cart-modal .cart-items .empty-cart-message::before {
  content: "🛒";
  display: block;
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}
.cart-modal .cart-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid var(--medium-gray);
  background-color: #fff;
  margin-bottom: 10px;
  border-radius: 8px;
  transition: var(--transition);
}
.cart-modal .cart-item:hover {
  background-color: rgba(249, 249, 249, 0.6784313725);
}
.cart-modal .cart-item .cart-item-image {
  width: 70px;
  height: 70px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #F5F5F5;
  padding: 5px;
  border: 1px solid #e0e0e0;
}
.cart-modal .cart-item .cart-item-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.cart-modal .cart-item .cart-item-details {
  flex: 1;
}
.cart-modal .cart-item .cart-item-details .cart-item-name {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 0.95rem;
  color: #333;
}
.cart-modal .cart-item .cart-item-details .cart-item-price {
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 1rem;
}
.cart-modal .cart-item .cart-item-details .cart-item-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}
.cart-modal .cart-item .cart-item-details .cart-item-controls .quantity-control {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  overflow: hidden;
  width: -moz-fit-content;
  width: fit-content;
  background-color: #F8F8F8;
  padding: 2px 2px;
  align-items: center;
  justify-content: center;
}
.cart-modal .cart-item .cart-item-details .cart-item-controls .quantity-control button {
  width: 25px;
  height: 25px;
  background-color: white;
  border: 0.5px solid rgba(126, 124, 124, 0.631372549);
  font-weight: bold;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
}
.cart-modal .cart-item .cart-item-details .cart-item-controls .quantity-control button:hover {
  background-color: #111;
  color: white;
}
.cart-modal .cart-item .cart-item-details .cart-item-controls .quantity-control span {
  font-weight: 600;
  padding: 0 10px;
  min-width: 30px;
  text-align: center;
}
.cart-modal .cart-item .cart-item-details .cart-item-controls .remove-item {
  color: #ff3b30;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.85rem;
  transition: var(--transition);
  padding: 5px 10px;
  border-radius: 4px;
  margin-left: auto;
}
.cart-modal .cart-item .cart-item-details .cart-item-controls .remove-item:hover {
  background-color: rgba(255, 59, 48, 0.1);
}
.cart-modal .cart-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--medium-gray);
}
.cart-modal .cart-footer .cart-total {
  display: flex;
  justify-content: space-between;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
}
.cart-modal .cart-footer .checkout-btn {
  background-color: var(--secondary-color);
  color: white;
  padding: 15px;
  width: 100%;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
}
.cart-modal .cart-footer .checkout-btn:hover {
  background-color: var(--accent-color);
}

/* Toast Notification */
.toast-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #4CAF50;
  border-radius: 50px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1000;
  letter-spacing: 0.5px;
}
.toast-notification.show {
  transform: translateY(0);
  opacity: 1;
}
.toast-notification.success {
  border-left: 4px solid #4CAF50;
}
.toast-notification .toast-content {
  display: flex;
  align-items: center;
  gap: 10px;
}
.toast-notification .toast-content .toast-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}
.toast-notification .toast-content .toast-message {
  font-weight: 600;
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  main header #hero .hero-content .hero-text h1 {
    font-size: 3.5rem;
  }
}
@media (max-width: 992px) {
  main header nav #logo {
    margin-right: 2rem;
  }
  main header nav #nav-middle {
    gap: 1.5rem;
  }
  main header #hero .hero-content .hero-text h1 {
    font-size: 3rem;
  }
  main header #hero .hero-content .hero-text p {
    font-size: 1.1rem;
  }
  main #gallery-price .gallery-container {
    flex-direction: column;
  }
}
@media (max-width: 768px) {
  main header nav {
    padding: 0.5rem;
  }
  main header nav #logo {
    margin-right: 1rem;
  }
  main header nav #nav-middle {
    display: none;
  }
  main header #hero .hero-content {
    flex-direction: column;
    text-align: center;
    padding-top: 50px;
  }
  main header #hero .hero-content .hero-text {
    padding-right: 0;
    margin-bottom: 30px;
  }
  main header #hero .hero-content .hero-text h1::after {
    left: 50%;
    transform: translateX(-50%);
  }
  main header #hero .hero-content .hero-text p {
    margin: 0 auto 30px;
  }
  main #features .features-container .feature {
    min-width: 100%;
  }
  main #gallery-price .gallery-container .gallery-left {
    flex-direction: column;
  }
  main #gallery-price .gallery-container .gallery-left .thumbnails {
    flex-direction: row;
    overflow-x: auto;
  }
  main #gallery-price .gallery-container .gallery-left .thumbnails .thumbnail {
    width: 60px;
    height: 60px;
  }
  .cart-modal {
    width: 100%;
    right: -100%;
  }
}
@media (max-width: 576px) {
  main header #hero .hero-content .hero-text h1 {
    font-size: 2.5rem;
  }
  main header #hero .hero-content .hero-text p {
    font-size: 1rem;
  }
  main #womens-collection .products-grid {
    grid-template-columns: 1fr;
  }
  footer .footer-container {
    flex-direction: column;
    gap: 30px;
  }
  footer .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}/*# sourceMappingURL=women.css.map */
// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Helvetica+Now+Display:wght@300;400;500;600;700&display=swap');

// Variables
:root {
  --primary-color: #111;
  --secondary-color: #4361ee;
  --accent-color: #3a56d4;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --light-bg: #f8f9fa;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --border-radius: 12px;
  --box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// Reset and Base Styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: hidden;
}

// Login Container
.login-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

// Background Shapes
.background-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;

  .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// Login Card
.login-card {
  position: relative;
  z-index: 1;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  width: 100%;
  max-width: 900px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    max-width: 400px;
  }
}

// Logo Section
.logo-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--gray-800) 100%);
  color: var(--white);
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../assets/images/pattern.svg') no-repeat center center;
    background-size: cover;
    opacity: 0.1;
  }

  .logo {
    width: 120px;
    height: auto;
    margin-bottom: 30px;
    filter: brightness(0) invert(1);
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
  }

  @media (max-width: 768px) {
    padding: 40px 20px;
    
    h1 {
      font-size: 2rem;
    }
  }
}

// Form Container
.form-container {
  padding: 60px 40px;
  position: relative;

  @media (max-width: 768px) {
    padding: 40px 20px;
  }
}

// Auth Forms
.auth-form {
  display: none;
  animation: fadeIn 0.5s ease-in-out;

  &.active {
    display: block;
  }

  h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
    text-align: center;
  }

  .form-description {
    color: var(--gray-600);
    text-align: center;
    margin-bottom: 30px;
    line-height: 1.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Form Groups
.form-group {
  position: relative;
  margin-bottom: 25px;

  label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 8px;
  }

  input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: 2px solid var(--gray-300);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Helvetica Now Display', sans-serif;
    transition: var(--transition);
    background: var(--white);

    &:focus {
      outline: none;
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    }

    &:invalid {
      border-color: var(--error-color);
    }
  }

  .input-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    font-size: 1.2rem;
    pointer-events: none;
  }
}

// Password Field Specific Styles
.password-field {
  .password-input-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  input {
    width: 100%;
    padding: 15px 50px 15px 15px;
    border: 2px solid var(--gray-300);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Helvetica Now Display', sans-serif;
    transition: var(--transition);
    background: var(--white);

    &:focus {
      outline: none;
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    }

    &:invalid {
      border-color: var(--error-color);
    }
  }

  .password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
    transition: var(--transition);
    z-index: 10;

    &:hover {
      color: var(--primary-color);
    }

    i {
      display: block;
    }
  }
}

// Form Options
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

// Checkbox Container
.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--gray-700);

  input[type="checkbox"] {
    display: none;
  }

  .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: var(--transition);

    &::after {
      content: '';
      position: absolute;
      left: 4px;
      top: 0px;
      width: 5px;
      height: 9px;
      border: solid var(--white);
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: var(--transition);
    }
  }

  input[type="checkbox"]:checked + .checkmark {
    background: var(--secondary-color);
    border-color: var(--secondary-color);

    &::after {
      opacity: 1;
    }
  }

  .terms-link {
    color: var(--secondary-color);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Forgot Password Link
.forgot-password {
  color: var(--secondary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);

  &:hover {
    text-decoration: underline;
  }
}

// Auth Buttons
.auth-btn {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Helvetica Now Display', sans-serif;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 15px;

  &.primary {
    background: var(--secondary-color);
    color: var(--white);

    &:hover {
      background: var(--accent-color);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
    }

    &:disabled {
      background: var(--gray-400);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }

  &.google {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);

    &:hover {
      background: var(--gray-100);
      border-color: var(--gray-400);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    i {
      font-size: 1.2rem;
      color: #db4437;
    }
  }
}

// Divider
.divider {
  position: relative;
  text-align: center;
  margin: 25px 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-300);
  }

  span {
    background: var(--white);
    padding: 0 20px;
    color: var(--gray-500);
    font-size: 0.9rem;
  }
}

// Switch Form
.switch-form {
  text-align: center;
  margin-top: 25px;
  color: var(--gray-600);
  font-size: 0.9rem;

  a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);

    &:hover {
      text-decoration: underline;
    }
  }
}

// Loading Overlay
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);

  &.active {
    display: flex;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  p {
    color: var(--white);
    font-size: 1.1rem;
    font-weight: 500;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Notification Container
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
}

.notification {
  background: var(--white);
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;

  &.success {
    border-left-color: var(--success-color);

    .notification-icon {
      color: var(--success-color);
    }
  }

  &.error {
    border-left-color: var(--error-color);

    .notification-icon {
      color: var(--error-color);
    }
  }

  &.warning {
    border-left-color: var(--warning-color);

    .notification-icon {
      color: var(--warning-color);
    }
  }

  &.info {
    border-left-color: var(--secondary-color);

    .notification-icon {
      color: var(--secondary-color);
    }
  }

  .notification-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
  }

  .notification-content {
    flex: 1;

    .notification-title {
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: 2px;
    }

    .notification-message {
      color: var(--gray-600);
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }

  .notification-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
    transition: var(--transition);

    &:hover {
      color: var(--gray-700);
    }
  }

  .notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: currentColor;
    opacity: 0.3;
    animation: progress 5s linear forwards;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .login-card {
    margin: 20px;
    border-radius: 8px;
  }

  .logo-section {
    h1 {
      font-size: 1.8rem;
    }

    .subtitle {
      font-size: 1rem;
    }
  }

  .auth-form h2 {
    font-size: 1.5rem;
  }

  .notification-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .form-container {
    padding: 30px 20px;
  }

  .logo-section {
    padding: 30px 20px;
  }
}

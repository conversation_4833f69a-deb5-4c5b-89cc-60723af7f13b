// Authentication utility functions

// API Configuration
const API_BASE_URL = 'http://localhost:4000/api';

// Get auth token from localStorage
export function getAuthToken() {
    return localStorage.getItem('authToken');
}

// Get user data from localStorage
export function getUserData() {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
}

// Check if user is authenticated
export function isAuthenticated() {
    const token = getAuthToken();
    const user = getUserData();
    return !!(token && user);
}

// Logout user
export function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    
    // Show logout message
    showToast('Logged out successfully', 'success');
    
    // Redirect to login page after a short delay
    setTimeout(() => {
        window.location.href = './Pages/login.html';
    }, 1500);
}

// Verify token with server
export async function verifyToken() {
    const token = getAuthToken();
    
    if (!token) {
        return false;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/user/me`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        
        if (response.ok && result.success) {
            // Update user data in localStorage
            localStorage.setItem('user', JSON.stringify(result.user));
            return true;
        } else {
            // Token is invalid, clear auth data
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            return false;
        }
    } catch (error) {
        console.error('Token verification error:', error);
        return false;
    }
}

// Initialize authentication UI
export function initAuthUI() {
    const userIcon = document.querySelector('.user-icon');
    const authButtons = document.querySelector('.auth-buttons');
    const cartIcon = document.querySelector('.cart-icon-container');
    
    if (isAuthenticated()) {
        // User is logged in
        const userData = getUserData();
        
        // Hide auth buttons if they exist
        if (authButtons) {
            authButtons.style.display = 'none';
        }
        
        // Show user icon if it doesn't exist, create it
        if (!userIcon && cartIcon) {
            createUserIcon(userData, cartIcon);
        }
        
        // Update existing user icon
        if (userIcon) {
            updateUserIcon(userData, userIcon);
        }
    } else {
        // User is not logged in
        
        // Hide user icon if it exists
        if (userIcon) {
            userIcon.style.display = 'none';
        }
        
        // Show auth buttons if they exist
        if (authButtons) {
            authButtons.style.display = 'flex';
        } else if (cartIcon) {
            // Create auth buttons if they don't exist
            createAuthButtons(cartIcon);
        }
    }
}

// Create user icon with dropdown
function createUserIcon(userData, insertAfter) {
    const userIconHTML = `
        <div class="user-icon-container">
            <div class="user-icon">
                <i class="ph ph-user-circle"></i>
                <span class="user-name">${userData.name.split(' ')[0]}</span>
            </div>
            <div class="user-dropdown">
                <div class="dropdown-header">
                    <div class="user-avatar">
                        <i class="ph ph-user-circle"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-full-name">${userData.name}</div>
                        <div class="user-email">${userData.email}</div>
                    </div>
                </div>
                <div class="dropdown-menu">
                    <a href="#" class="dropdown-item">
                        <i class="ph ph-user"></i>
                        <span>Profile</span>
                    </a>
                    <a href="#" class="dropdown-item">
                        <i class="ph ph-shopping-bag"></i>
                        <span>Orders</span>
                    </a>
                    <a href="#" class="dropdown-item">
                        <i class="ph ph-gear"></i>
                        <span>Settings</span>
                    </a>
                    <div class="dropdown-divider"></div>
                    <button class="dropdown-item logout-btn">
                        <i class="ph ph-sign-out"></i>
                        <span>Logout</span>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    insertAfter.insertAdjacentHTML('afterend', userIconHTML);
    
    // Add event listeners
    setupUserIconEvents();
}

// Update existing user icon
function updateUserIcon(userData, userIcon) {
    const userName = userIcon.querySelector('.user-name');
    const userFullName = userIcon.querySelector('.user-full-name');
    const userEmail = userIcon.querySelector('.user-email');
    
    if (userName) userName.textContent = userData.name.split(' ')[0];
    if (userFullName) userFullName.textContent = userData.name;
    if (userEmail) userEmail.textContent = userData.email;
    
    userIcon.style.display = 'flex';
    
    // Setup events if not already done
    setupUserIconEvents();
}

// Create auth buttons
function createAuthButtons(insertAfter) {
    const authButtonsHTML = `
        <div class="auth-buttons">
            <a href="./Pages/login.html" class="login-btn">Login</a>
            <a href="./Pages/login.html?tab=register" class="signup-btn">Sign Up</a>
        </div>
    `;
    
    insertAfter.insertAdjacentHTML('afterend', authButtonsHTML);
}

// Setup user icon events
function setupUserIconEvents() {
    const userIcon = document.querySelector('.user-icon');
    const userDropdown = document.querySelector('.user-dropdown');
    const logoutBtn = document.querySelector('.logout-btn');
    
    if (userIcon && userDropdown) {
        // Toggle dropdown on click
        userIcon.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('active');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!userIcon.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('active');
            }
        });
    }
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            logout();
        });
    }
}

// Protect page (redirect to login if not authenticated)
export function protectPage(redirectUrl = null) {
    if (!isAuthenticated()) {
        const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
        const loginUrl = redirectUrl || `./Pages/login.html?redirect=${currentUrl}`;
        window.location.href = loginUrl;
        return false;
    }
    return true;
}

// Show toast notification (if not already defined)
function showToast(message, type = 'success') {
    // Check if toast function exists in cart.js or other files
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
        return;
    }
    
    // Create simple toast if no other toast system exists
    const toast = document.createElement('div');
    toast.className = `simple-toast ${type}`;
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#ffc107'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(400px);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // Hide toast
    setTimeout(() => {
        toast.style.transform = 'translateX(400px)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Initialize auth on page load
document.addEventListener('DOMContentLoaded', () => {
    // Verify token and update UI
    verifyToken().then(isValid => {
        if (isValid || isAuthenticated()) {
            initAuthUI();
        } else {
            initAuthUI(); // This will show login buttons
        }
    });
});

// Export for use in other modules
export { initAuthUI, showToast };

/**
 * Authentication JavaScript
 * Handles login, signup, forgot password, and Google OAuth functionality
 */

// API Configuration
const API_BASE_URL = 'http://localhost:4000/api';

// Authentication state
let isAuthenticated = false;
let currentUser = null;

// DOM Elements
let loginForm, signupForm, forgotPasswordForm;
let loadingOverlay, notificationContainer;

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeAuth();
    setupEventListeners();
    checkAuthStatus();
    initializeGoogleOAuth();
});

/**
 * Initialize authentication elements
 */
function initializeAuth() {
    // Get form elements
    loginForm = document.getElementById('loginForm');
    signupForm = document.getElementById('signupForm');
    forgotPasswordForm = document.getElementById('forgotPasswordForm');
    loadingOverlay = document.getElementById('loadingOverlay');
    notificationContainer = document.getElementById('notificationContainer');

    console.log('Authentication system initialized');
}

/**
 * Setup event listeners for forms
 */
function setupEventListeners() {
    // Login form
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Signup form
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
    }

    // Forgot password form
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }

    // Google OAuth buttons
    const googleSignInBtn = document.getElementById('googleSignIn');
    const googleSignUpBtn = document.getElementById('googleSignUp');

    if (googleSignInBtn) {
        googleSignInBtn.addEventListener('click', handleGoogleAuth);
    }

    if (googleSignUpBtn) {
        googleSignUpBtn.addEventListener('click', handleGoogleAuth);
    }
}

/**
 * Check if user is already authenticated
 */
function checkAuthStatus() {
    const token = localStorage.getItem('stepstyle-token');
    const user = localStorage.getItem('stepstyle-user');

    if (token && user) {
        try {
            currentUser = JSON.parse(user);
            isAuthenticated = true;
            
            // Check if we should redirect to a specific page
            const redirectUrl = localStorage.getItem('stepstyle-redirect-url');
            if (redirectUrl) {
                localStorage.removeItem('stepstyle-redirect-url');
                window.location.href = redirectUrl;
            } else {
                // Redirect to home page
                window.location.href = '../index.html';
            }
        } catch (error) {
            console.error('Error parsing user data:', error);
            clearAuthData();
        }
    }
}

/**
 * Handle login form submission
 */
async function handleLogin(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const email = formData.get('email');
    const password = formData.get('password');
    const rememberMe = document.getElementById('rememberMe').checked;

    // Validate form
    if (!email || !password) {
        showNotification('Please fill in all fields', 'error');
        return;
    }

    try {
        showLoading(true);
        
        const response = await fetch(`${API_BASE_URL}/user/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (data.success) {
            // Store authentication data
            localStorage.setItem('stepstyle-token', data.token);
            localStorage.setItem('stepstyle-user', JSON.stringify(data.user));
            
            if (rememberMe) {
                localStorage.setItem('stepstyle-remember', 'true');
            }

            showNotification('Login successful! Redirecting...', 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                const redirectUrl = localStorage.getItem('stepstyle-redirect-url');
                if (redirectUrl) {
                    localStorage.removeItem('stepstyle-redirect-url');
                    window.location.href = redirectUrl;
                } else {
                    window.location.href = '../index.html';
                }
            }, 1500);

        } else {
            showNotification(data.message || 'Login failed', 'error');
        }

    } catch (error) {
        console.error('Login error:', error);
        showNotification('Network error. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Handle signup form submission
 */
async function handleSignup(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const name = formData.get('name');
    const email = formData.get('email');
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    const agreeTerms = document.getElementById('agreeTerms').checked;

    // Validate form
    if (!name || !email || !password || !confirmPassword) {
        showNotification('Please fill in all fields', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showNotification('Passwords do not match', 'error');
        return;
    }

    if (password.length < 8) {
        showNotification('Password must be at least 8 characters long', 'error');
        return;
    }

    if (!agreeTerms) {
        showNotification('Please agree to the Terms & Conditions', 'error');
        return;
    }

    try {
        showLoading(true);
        
        const response = await fetch(`${API_BASE_URL}/user/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name, email, password }),
        });

        const data = await response.json();

        if (data.success) {
            // Store authentication data
            localStorage.setItem('stepstyle-token', data.token);
            localStorage.setItem('stepstyle-user', JSON.stringify(data.user));

            showNotification('Account created successfully! Redirecting...', 'success');
            
            // Redirect to home page after short delay
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1500);

        } else {
            showNotification(data.message || 'Registration failed', 'error');
        }

    } catch (error) {
        console.error('Signup error:', error);
        showNotification('Network error. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Handle forgot password form submission
 */
async function handleForgotPassword(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const email = formData.get('email');

    // Validate email
    if (!email) {
        showNotification('Please enter your email address', 'error');
        return;
    }

    try {
        showLoading(true);
        
        const response = await fetch(`${API_BASE_URL}/user/forgot-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email }),
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
            // Switch back to login form
            setTimeout(() => {
                switchToLogin();
            }, 2000);
        } else {
            showNotification(data.message || 'Failed to send reset email', 'error');
        }

    } catch (error) {
        console.error('Forgot password error:', error);
        showNotification('Network error. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Initialize Google OAuth
 */
function initializeGoogleOAuth() {
    // Initialize Google OAuth when the API is loaded
    if (typeof google !== 'undefined') {
        google.accounts.id.initialize({
            client_id: '************-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com',
            callback: handleGoogleCallback
        });
    } else {
        // Retry after a short delay if Google API is not loaded yet
        setTimeout(initializeGoogleOAuth, 1000);
    }
}

/**
 * Handle Google OAuth button click
 */
function handleGoogleAuth() {
    if (typeof google !== 'undefined') {
        google.accounts.id.prompt();
    } else {
        showNotification('Google authentication is not available', 'error');
    }
}

/**
 * Handle Google OAuth callback
 */
async function handleGoogleCallback(response) {
    try {
        showLoading(true);
        
        const serverResponse = await fetch(`${API_BASE_URL}/user/google-login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token: response.credential }),
        });

        const data = await serverResponse.json();

        if (data.success) {
            // Store authentication data
            localStorage.setItem('stepstyle-token', data.token);
            localStorage.setItem('stepstyle-user', JSON.stringify(data.user));

            showNotification('Google authentication successful! Redirecting...', 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                const redirectUrl = localStorage.getItem('stepstyle-redirect-url');
                if (redirectUrl) {
                    localStorage.removeItem('stepstyle-redirect-url');
                    window.location.href = redirectUrl;
                } else {
                    window.location.href = '../index.html';
                }
            }, 1500);

        } else {
            showNotification(data.message || 'Google authentication failed', 'error');
        }

    } catch (error) {
        console.error('Google auth error:', error);
        showNotification('Google authentication failed', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Clear authentication data
 */
function clearAuthData() {
    localStorage.removeItem('stepstyle-token');
    localStorage.removeItem('stepstyle-user');
    localStorage.removeItem('stepstyle-remember');
    isAuthenticated = false;
    currentUser = null;
}

/**
 * Show/hide loading overlay
 */
function showLoading(show) {
    if (loadingOverlay) {
        if (show) {
            loadingOverlay.classList.add('active');
        } else {
            loadingOverlay.classList.remove('active');
        }
    }
}

// Export functions for use in other files
window.authFunctions = {
    checkAuthStatus,
    clearAuthData,
    isAuthenticated: () => isAuthenticated,
    getCurrentUser: () => currentUser
};

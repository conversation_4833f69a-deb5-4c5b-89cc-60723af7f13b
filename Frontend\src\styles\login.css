@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
:root {
  --primary-color: #111;
  --secondary-color: #4361ee;
  --accent-color: #3a56d4;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --light-bg: #f8f9fa;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --dark-bg: #2c2c2c;
  --red-primary: #ff4757;
  --border-radius: 12px;
  --box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: #f5f5f5;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: hidden;
}

.login-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
}

.left-section {
  flex: 1;
  background: var(--dark-bg);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
  position: relative;
  overflow: hidden;
}
.left-section .brand-header h1 {
  color: white;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 1px;
}
.left-section .shoe-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.left-section .shoe-container .animated-shoe {
  width: 300px;
  height: auto;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
  animation: shoeFloat 3s ease-in-out infinite;
  transition: transform 0.3s ease;
}
.left-section .shoe-container .animated-shoe:hover {
  transform: scale(1.05) rotate(5deg);
}
.left-section .brand-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.left-section .brand-footer .brand-info h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}
.left-section .brand-footer .brand-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}
.left-section .brand-footer .social-icons {
  display: flex;
  gap: 12px;
}
.left-section .brand-footer .social-icons .social-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.left-section .brand-footer .social-icons .social-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}
.left-section .brand-footer .social-icons .social-btn i {
  font-size: 16px;
}

@keyframes shoeFloat {
  0%, 100% {
    transform: translateY(0px) rotate(-5deg);
  }
  50% {
    transform: translateY(-15px) rotate(5deg);
  }
}
.right-section {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  padding: 40px;
  position: relative;
}
.right-section .language-selector {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--gray-600);
  cursor: pointer;
}
.right-section .language-selector span {
  font-size: 14px;
  font-weight: 500;
}
.right-section .language-selector i {
  font-size: 12px;
}
.right-section .form-header {
  margin-bottom: 40px;
  margin-top: 60px;
}
.right-section .form-header h1 {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
  letter-spacing: 1px;
}
.right-section .form-header h2 {
  font-size: 32px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}
.right-section .form-header p {
  color: var(--gray-600);
  font-size: 16px;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.auth-form {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}
.auth-form.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.form-group {
  position: relative;
  margin-bottom: 24px;
}
.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 8px;
}
.form-group input {
  width: 100%;
  padding: 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  font-size: 16px;
  font-family: "Inter", sans-serif;
  transition: var(--transition);
  background: #f8f9fa;
}
.form-group input::-moz-placeholder {
  color: var(--gray-500);
}
.form-group input::placeholder {
  color: var(--gray-500);
}
.form-group input:focus {
  outline: none;
  border-color: var(--secondary-color);
  background: white;
}

.password-field .password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}
.password-field input {
  width: 100%;
  padding: 16px 50px 16px 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  font-size: 16px;
  font-family: "Inter", sans-serif;
  transition: var(--transition);
  background: #f8f9fa;
}
.password-field input::-moz-placeholder {
  color: var(--gray-500);
}
.password-field input::placeholder {
  color: var(--gray-500);
}
.password-field input:focus {
  outline: none;
  border-color: var(--secondary-color);
  background: white;
}
.password-field .password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  transition: var(--transition);
  z-index: 10;
}
.password-field .password-toggle:hover {
  color: var(--primary-color);
}
.password-field .password-toggle i {
  display: block;
}

.forgot-link {
  margin-bottom: 24px;
}
.forgot-link a {
  color: #6366f1;
  text-decoration: none;
  font-size: 14px;
}
.forgot-link a:hover {
  text-decoration: underline;
}

.google-btn {
  width: 100%;
  padding: 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  background: white;
  color: var(--gray-700);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}
.google-btn:hover {
  background: #f8f9fa;
  border-color: var(--gray-400);
}
.google-btn i {
  font-size: 20px;
  color: #db4437;
}

.login-btn {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  background: var(--red-primary);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  margin-bottom: 24px;
}
.login-btn:hover {
  background: #ff3742;
  transform: translateY(-1px);
}
.login-btn:active {
  transform: translateY(0);
}

.signup-link {
  text-align: center;
  color: var(--gray-600);
  font-size: 14px;
}
.signup-link a {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
}
.signup-link a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  .left-section {
    flex: none;
    height: 40vh;
    padding: 20px;
  }
  .left-section .shoe-container .animated-shoe {
    width: 200px;
  }
  .left-section .brand-footer .brand-info h3 {
    font-size: 16px;
  }
  .left-section .brand-footer .brand-info p {
    font-size: 12px;
  }
  .left-section .brand-footer .social-icons .social-btn {
    width: 35px;
    height: 35px;
  }
  .left-section .brand-footer .social-icons .social-btn i {
    font-size: 14px;
  }
  .right-section {
    flex: none;
    padding: 20px;
  }
  .right-section .form-header {
    margin-top: 20px;
    margin-bottom: 30px;
  }
  .right-section .form-header h1 {
    font-size: 20px;
  }
  .right-section .form-header h2 {
    font-size: 28px;
  }
  .right-section .form-header p {
    font-size: 14px;
  }
}
@media (max-width: 480px) {
  .left-section {
    height: 35vh;
    padding: 15px;
  }
  .left-section .brand-header h1 {
    font-size: 20px;
  }
  .left-section .shoe-container .animated-shoe {
    width: 150px;
  }
  .right-section {
    padding: 15px;
  }
  .right-section .form-header h1 {
    font-size: 18px;
  }
  .right-section .form-header h2 {
    font-size: 24px;
  }
  .form-group {
    margin-bottom: 20px;
  }
  .form-group input {
    padding: 14px;
    font-size: 14px;
  }
  .password-field input {
    padding: 14px 45px 14px 14px;
  }
  .google-btn,
  .login-btn {
    padding: 14px;
    font-size: 14px;
  }
}
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.loading-overlay.active {
  display: flex;
}
.loading-overlay .loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}
.loading-overlay p {
  color: var(--white);
  font-size: 1.1rem;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
}

.notification {
  background: var(--white);
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}
.notification.success {
  border-left-color: var(--success-color);
}
.notification.success .notification-icon {
  color: var(--success-color);
}
.notification.error {
  border-left-color: var(--error-color);
}
.notification.error .notification-icon {
  color: var(--error-color);
}
.notification.warning {
  border-left-color: var(--warning-color);
}
.notification.warning .notification-icon {
  color: var(--warning-color);
}
.notification.info {
  border-left-color: var(--secondary-color);
}
.notification.info .notification-icon {
  color: var(--secondary-color);
}
.notification .notification-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}
.notification .notification-content {
  flex: 1;
}
.notification .notification-content .notification-title {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 2px;
}
.notification .notification-content .notification-message {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
}
.notification .notification-close {
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
  transition: var(--transition);
}
.notification .notification-close:hover {
  color: var(--gray-700);
}
.notification .notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.3;
  animation: progress 5s linear forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}/*# sourceMappingURL=login.css.map */
/* Import fonts and base styles */
@import url('https://fonts.googleapis.com/css2?family=Helvetica+Now+Display:wght@300;400;500;600;700&display=swap');

/* CSS Variables - matching StepStyle theme */
:root {
  --primary-color: #111111;
  --secondary-color: #ff5722;
  --accent-color: #fcce80;
  --text-color: #333333;
  --text-light: #666666;
  --text-muted: #999999;
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --medium-gray: #e9ecef;
  --dark-gray: #6c757d;
  --border-color: #dee2e6;
  --success-color: #28a745;
  --error-color: #dc3545;
  --warning-color: #ffc107;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
  --border-radius: 8px;
  --transition: all 0.3s ease;
  --font-primary: 'Helvetica Now Display', 'Helvetica Neue', Arial, sans-serif;
  --font-secondary: 'Funnel Display Medium', sans-serif;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background-color: var(--white);
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Navigation styles (matching main site) */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
}

nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

#logo {
  height: 40px;
  width: auto;
}

#logo img {
  height: 100%;
  width: auto;
  object-fit: contain;
}

#nav-middle {
  display: flex;
  align-items: center;
  gap: 2rem;
}

#nav-middle a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.95rem;
  transition: var(--transition);
  position: relative;
}

#nav-middle a:hover {
  color: var(--secondary-color);
}

#nav-last {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

#search-bar {
  display: flex;
  align-items: center;
  background-color: var(--light-gray);
  border-radius: 25px;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

#search-bar:focus-within {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.search-icon {
  color: var(--text-muted);
  margin-right: 0.5rem;
}

#nav-search {
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.9rem;
  width: 200px;
  color: var(--text-color);
}

.cart-icon-container {
  position: relative;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition);
}

.cart-icon-container:hover {
  background-color: var(--light-gray);
}

.cart {
  font-size: 1.5rem;
  color: var(--text-color);
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--secondary-color);
  color: var(--white);
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

/* Auth Section */
.auth-section {
  min-height: 100vh;
  padding-top: 80px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.auth-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: calc(100vh - 80px);
}

/* Background Design Elements */
.auth-bg-design {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-circle-1 {
  position: absolute;
  top: 10%;
  right: 10%;
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, var(--accent-color), #FAA484);
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
}

.bg-circle-2 {
  position: absolute;
  bottom: 20%;
  left: 5%;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, var(--secondary-color), #ff8a65);
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.2;
}

.bg-gradient {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 60%;
  background: linear-gradient(45deg, rgba(252, 206, 128, 0.1), rgba(255, 87, 34, 0.1));
  border-radius: 50%;
  filter: blur(120px);
}

/* Auth Forms Container */
.auth-forms-container {
  background: var(--white);
  border-radius: 20px;
  box-shadow: var(--shadow-hover);
  padding: 3rem;
  position: relative;
  z-index: 2;
  max-width: 500px;
  width: 100%;
  margin: 0 auto;
}

/* Tab Navigation */
.auth-tabs {
  display: flex;
  background-color: var(--light-gray);
  border-radius: 12px;
  padding: 0.5rem;
  margin-bottom: 2rem;
}

.auth-tab {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: var(--text-muted);
  font-weight: 500;
  font-size: 0.95rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
  font-family: var(--font-primary);
}

.auth-tab.active {
  background-color: var(--white);
  color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Form Wrapper */
.auth-form-wrapper {
  display: none;
}

.auth-form-wrapper.active {
  display: block;
  animation: fadeInUp 0.4s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Header */
.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.form-header p {
  color: var(--text-light);
  font-size: 0.95rem;
}

/* Form Styles */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 2.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  font-family: var(--font-primary);
  transition: var(--transition);
  background-color: var(--white);
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.input-wrapper input.error {
  border-color: var(--error-color);
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-muted);
  font-size: 1.1rem;
  z-index: 1;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 1.1rem;
  padding: 0.25rem;
  border-radius: 4px;
  transition: var(--transition);
}

.password-toggle:hover {
  color: var(--text-color);
  background-color: var(--light-gray);
}

.error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: none;
}

.error-message.show {
  display: block;
}

/* Password Strength */
.password-strength {
  margin-top: 0.5rem;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: var(--medium-gray);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.strength-fill {
  height: 100%;
  width: 0%;
  transition: var(--transition);
  border-radius: 2px;
}

.strength-fill.weak {
  width: 25%;
  background-color: var(--error-color);
}

.strength-fill.fair {
  width: 50%;
  background-color: var(--warning-color);
}

.strength-fill.good {
  width: 75%;
  background-color: #17a2b8;
}

.strength-fill.strong {
  width: 100%;
  background-color: var(--success-color);
}

.strength-text {
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-color);
}

.checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  position: relative;
  transition: var(--transition);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-size: 0.8rem;
  font-weight: 600;
}

.forgot-password,
.terms-link {
  color: var(--secondary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);
}

.forgot-password:hover,
.terms-link:hover {
  text-decoration: underline;
}

/* Buttons */
.auth-btn {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  font-weight: 600;
  font-family: var(--font-primary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.auth-btn.primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.auth-btn.primary:hover {
  background-color: #000000;
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.auth-btn.secondary {
  background-color: transparent;
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.auth-btn.secondary:hover {
  background-color: var(--light-gray);
  border-color: var(--text-color);
}

.auth-btn.google {
  background-color: var(--white);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.auth-btn.google:hover {
  background-color: var(--light-gray);
  border-color: var(--text-color);
}

.auth-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Button Loader */
.btn-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border-color);
}

.divider span {
  background-color: var(--white);
  padding: 0 1rem;
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* Brand Showcase */
.brand-showcase {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
}

.showcase-content {
  max-width: 400px;
}

.brand-logo {
  margin-bottom: 2rem;
}

.brand-logo img {
  height: 60px;
  width: auto;
}

.showcase-content h3 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.showcase-content p {
  color: var(--text-light);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.showcase-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.feature i {
  font-size: 1.5rem;
  color: var(--secondary-color);
}

.feature span {
  font-size: 0.9rem;
  color: var(--text-color);
  font-weight: 500;
}

.showcase-image {
  margin-top: 2rem;
}

.showcase-image img {
  max-width: 300px;
  height: auto;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(-15deg);
  }
  50% {
    transform: translateY(-20px) rotate(-15deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .auth-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1rem;
  }
  
  .brand-showcase {
    order: -1;
  }
  
  .showcase-content h3 {
    font-size: 1.5rem;
  }
  
  .showcase-image img {
    max-width: 200px;
  }
}

@media (max-width: 768px) {
  nav {
    padding: 1rem;
  }
  
  #nav-middle {
    display: none;
  }
  
  #search-bar {
    width: 150px;
  }
  
  #nav-search {
    width: 100px;
  }
  
  .auth-forms-container {
    padding: 2rem 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-options {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .showcase-features {
    flex-direction: column;
    gap: 1rem;
  }
  
  .feature {
    flex-direction: row;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .auth-section {
    padding-top: 70px;
  }

  .auth-forms-container {
    padding: 1.5rem 1rem;
    margin: 1rem;
  }

  .form-header h2 {
    font-size: 1.5rem;
  }

  .auth-btn {
    padding: 1rem;
  }
}

/* Cart Modal Styles */
.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

.cart-modal {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: var(--white);
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  transition: right 0.4s ease;
  overflow-y: auto;
}

.cart-modal.open {
  right: 0;
}

.cart-modal-content {
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.cart-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.close-cart-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition);
}

.close-cart-btn:hover {
  background-color: var(--light-gray);
  color: var(--text-color);
}

.cart-items {
  flex: 1;
  overflow-y: auto;
}

.cart-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.checkout-btn {
  width: 100%;
  padding: 1rem;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.checkout-btn:hover {
  background-color: #000000;
  transform: translateY(-2px);
}

/* Toast Notification */
.toast-notification {
  position: fixed;
  top: 100px;
  right: 20px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-hover);
  padding: 1rem 1.5rem;
  z-index: 10000;
  transform: translateX(400px);
  transition: var(--transition);
  border-left: 4px solid var(--success-color);
}

.toast-notification.show {
  transform: translateX(0);
}

.toast-notification.success {
  border-left-color: var(--success-color);
}

.toast-notification.error {
  border-left-color: var(--error-color);
}

.toast-notification.warning {
  border-left-color: var(--warning-color);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.toast-icon {
  font-size: 1.2rem;
}

.toast-notification.success .toast-icon {
  color: var(--success-color);
}

.toast-notification.error .toast-icon {
  color: var(--error-color);
}

.toast-notification.warning .toast-icon {
  color: var(--warning-color);
}

.toast-message {
  font-weight: 500;
  color: var(--text-color);
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.login-btn,
.signup-btn {
  padding: 0.5rem 1rem;
  text-decoration: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition);
  font-family: var(--font-primary);
}

.login-btn {
  color: var(--text-color);
  border: 1px solid var(--border-color);
  background-color: transparent;
}

.login-btn:hover {
  background-color: var(--light-gray);
  border-color: var(--text-color);
}

.signup-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: 1px solid var(--primary-color);
}

.signup-btn:hover {
  background-color: #000000;
  transform: translateY(-1px);
}

/* User Icon and Dropdown */
.user-icon-container {
  position: relative;
}

.user-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.user-icon:hover {
  background-color: var(--light-gray);
}

.user-icon i {
  font-size: 1.5rem;
  color: var(--text-color);
}

.user-name {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-hover);
  border: 1px solid var(--border-color);
  min-width: 250px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition);
}

.user-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar i {
  font-size: 2rem;
  color: var(--text-muted);
}

.user-info {
  flex: 1;
}

.user-full-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.95rem;
}

.user-email {
  color: var(--text-muted);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.dropdown-menu {
  padding: 0.5rem 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-family: var(--font-primary);
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background-color: var(--light-gray);
}

.dropdown-item i {
  font-size: 1.1rem;
  color: var(--text-muted);
}

.dropdown-item.logout-btn {
  color: var(--error-color);
}

.dropdown-item.logout-btn i {
  color: var(--error-color);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 0.5rem 0;
}

@media (max-width: 768px) {
  .cart-modal {
    width: 100%;
    right: -100%;
  }

  .toast-notification {
    right: 10px;
    left: 10px;
    transform: translateY(-100px);
  }

  .toast-notification.show {
    transform: translateY(0);
  }

  .auth-buttons {
    gap: 0.5rem;
  }

  .login-btn,
  .signup-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .user-name {
    display: none;
  }

  .user-dropdown {
    right: -50px;
    min-width: 200px;
  }
}

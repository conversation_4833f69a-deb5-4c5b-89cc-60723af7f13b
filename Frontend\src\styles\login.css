@import url("https://fonts.googleapis.com/css2?family=Helvetica+Now+Display:wght@300;400;500;600;700&display=swap");
:root {
  --primary-color: #111;
  --secondary-color: #4361ee;
  --accent-color: #3a56d4;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --light-bg: #f8f9fa;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --border-radius: 12px;
  --box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Helvetica Now Display", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: hidden;
}

.login-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.background-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}
.background-shapes .shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}
.background-shapes .shape.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}
.background-shapes .shape.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}
.background-shapes .shape.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}
.login-card {
  position: relative;
  z-index: 1;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  width: 100%;
  max-width: 900px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}
@media (max-width: 768px) {
  .login-card {
    grid-template-columns: 1fr;
    max-width: 400px;
  }
}

.logo-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--gray-800) 100%);
  color: var(--white);
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}
.logo-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("../assets/images/pattern.svg") no-repeat center center;
  background-size: cover;
  opacity: 0.1;
}
.logo-section .logo {
  width: 120px;
  height: auto;
  margin-bottom: 30px;
  filter: brightness(0) invert(1);
}
.logo-section h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.logo-section .subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}
@media (max-width: 768px) {
  .logo-section {
    padding: 40px 20px;
  }
  .logo-section h1 {
    font-size: 2rem;
  }
}

.form-container {
  padding: 60px 40px;
  position: relative;
}
@media (max-width: 768px) {
  .form-container {
    padding: 40px 20px;
  }
}

.auth-form {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}
.auth-form.active {
  display: block;
}
.auth-form h2 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 10px;
  text-align: center;
}
.auth-form .form-description {
  color: var(--gray-600);
  text-align: center;
  margin-bottom: 30px;
  line-height: 1.5;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.form-group {
  position: relative;
  margin-bottom: 25px;
}
.form-group label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 8px;
}
.form-group input {
  width: 100%;
  padding: 15px 45px 15px 15px;
  border: 2px solid var(--gray-300);
  border-radius: 8px;
  font-size: 1rem;
  font-family: "Helvetica Now Display", sans-serif;
  transition: var(--transition);
  background: var(--white);
}
.form-group input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}
.form-group input:invalid {
  border-color: var(--error-color);
}
.form-group .input-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500);
  font-size: 1.2rem;
  pointer-events: none;
}
.form-group .password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
  transition: var(--transition);
}
.form-group .password-toggle:hover {
  color: var(--primary-color);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}
@media (max-width: 480px) {
  .form-options {
    flex-direction: column;
    align-items: flex-start;
  }
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--gray-700);
}
.checkbox-container input[type=checkbox] {
  display: none;
}
.checkbox-container .checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-300);
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: var(--transition);
}
.checkbox-container .checkmark::after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid var(--white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: var(--transition);
}
.checkbox-container input[type=checkbox]:checked + .checkmark {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}
.checkbox-container input[type=checkbox]:checked + .checkmark::after {
  opacity: 1;
}
.checkbox-container .terms-link {
  color: var(--secondary-color);
  text-decoration: none;
}
.checkbox-container .terms-link:hover {
  text-decoration: underline;
}

.forgot-password {
  color: var(--secondary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);
}
.forgot-password:hover {
  text-decoration: underline;
}

.auth-btn {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  font-family: "Helvetica Now Display", sans-serif;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 15px;
}
.auth-btn.primary {
  background: var(--secondary-color);
  color: var(--white);
}
.auth-btn.primary:hover {
  background: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}
.auth-btn.primary:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
.auth-btn.google {
  background: var(--white);
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}
.auth-btn.google:hover {
  background: var(--gray-100);
  border-color: var(--gray-400);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
.auth-btn.google i {
  font-size: 1.2rem;
  color: #db4437;
}

.divider {
  position: relative;
  text-align: center;
  margin: 25px 0;
}
.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gray-300);
}
.divider span {
  background: var(--white);
  padding: 0 20px;
  color: var(--gray-500);
  font-size: 0.9rem;
}

.switch-form {
  text-align: center;
  margin-top: 25px;
  color: var(--gray-600);
  font-size: 0.9rem;
}
.switch-form a {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}
.switch-form a:hover {
  text-decoration: underline;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}
.loading-overlay.active {
  display: flex;
}
.loading-overlay .loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}
.loading-overlay p {
  color: var(--white);
  font-size: 1.1rem;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
}

.notification {
  background: var(--white);
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}
.notification.success {
  border-left-color: var(--success-color);
}
.notification.success .notification-icon {
  color: var(--success-color);
}
.notification.error {
  border-left-color: var(--error-color);
}
.notification.error .notification-icon {
  color: var(--error-color);
}
.notification.warning {
  border-left-color: var(--warning-color);
}
.notification.warning .notification-icon {
  color: var(--warning-color);
}
.notification.info {
  border-left-color: var(--secondary-color);
}
.notification.info .notification-icon {
  color: var(--secondary-color);
}
.notification .notification-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}
.notification .notification-content {
  flex: 1;
}
.notification .notification-content .notification-title {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 2px;
}
.notification .notification-content .notification-message {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
}
.notification .notification-close {
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
  transition: var(--transition);
}
.notification .notification-close:hover {
  color: var(--gray-700);
}
.notification .notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.3;
  animation: progress 5s linear forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
@media (max-width: 768px) {
  .login-card {
    margin: 20px;
    border-radius: 8px;
  }
  .logo-section h1 {
    font-size: 1.8rem;
  }
  .logo-section .subtitle {
    font-size: 1rem;
  }
  .auth-form h2 {
    font-size: 1.5rem;
  }
  .notification-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  .form-container {
    padding: 30px 20px;
  }
  .logo-section {
    padding: 30px 20px;
  }
}

/*# sourceMappingURL=login.css.map */

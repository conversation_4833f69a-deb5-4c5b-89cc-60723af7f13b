// Import cart functionality
import { initCart } from './cart.js';

// API Configuration
const API_BASE_URL = 'http://localhost:4000/api';

// DOM Elements
const authTabs = document.querySelectorAll('.auth-tab');
const authForms = document.querySelectorAll('.auth-form-wrapper');
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const forgotPasswordForm = document.getElementById('forgotPasswordForm');
const forgotPasswordLink = document.getElementById('forgotPasswordLink');
const backToLoginBtn = document.querySelector('.back-to-login');
const passwordToggles = document.querySelectorAll('.password-toggle');
const passwordInput = document.getElementById('registerPassword');
const strengthBar = document.querySelector('.strength-fill');
const strengthText = document.querySelector('.strength-text');

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    initCart();
    initAuthSystem();
    checkAuthStatus();
});

// Initialize authentication system
function initAuthSystem() {
    // Tab switching
    authTabs.forEach(tab => {
        tab.addEventListener('click', () => switchTab(tab.dataset.tab));
    });

    // Form submissions
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }

    // Forgot password link
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', (e) => {
            e.preventDefault();
            showForgotPasswordForm();
        });
    }

    // Back to login button
    if (backToLoginBtn) {
        backToLoginBtn.addEventListener('click', () => switchTab('login'));
    }

    // Password toggles
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', () => togglePasswordVisibility(toggle));
    });

    // Password strength checker
    if (passwordInput) {
        passwordInput.addEventListener('input', checkPasswordStrength);
    }

    // Real-time validation
    setupRealTimeValidation();

    // Google OAuth buttons
    const googleLoginBtn = document.getElementById('googleLoginBtn');
    const googleRegisterBtn = document.getElementById('googleRegisterBtn');
    
    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', handleGoogleAuth);
    }
    
    if (googleRegisterBtn) {
        googleRegisterBtn.addEventListener('click', handleGoogleAuth);
    }
}

// Switch between tabs
function switchTab(tabName) {
    // Update tab buttons
    authTabs.forEach(tab => {
        tab.classList.toggle('active', tab.dataset.tab === tabName);
    });

    // Update form visibility
    authForms.forEach(form => {
        form.classList.toggle('active', form.id === `${tabName}-form`);
    });

    // Clear any error messages
    clearAllErrors();
}

// Show forgot password form
function showForgotPasswordForm() {
    authForms.forEach(form => {
        form.classList.remove('active');
    });
    document.getElementById('forgot-password-form').classList.add('active');
    
    // Hide tabs
    document.querySelector('.auth-tabs').style.display = 'none';
}

// Hide forgot password form and show tabs
function hideForgotPasswordForm() {
    document.querySelector('.auth-tabs').style.display = 'flex';
    switchTab('login');
}

// Toggle password visibility
function togglePasswordVisibility(toggle) {
    const targetId = toggle.dataset.target;
    const passwordField = document.getElementById(targetId);
    const icon = toggle.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.className = 'ri-eye-off-line';
    } else {
        passwordField.type = 'password';
        icon.className = 'ri-eye-line';
    }
}

// Check password strength
function checkPasswordStrength() {
    const password = passwordInput.value;
    let strength = 0;
    let strengthLabel = '';

    // Check password criteria
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    // Update strength bar and text
    strengthBar.className = 'strength-fill';
    
    switch (strength) {
        case 0:
        case 1:
            strengthBar.classList.add('weak');
            strengthLabel = 'Weak';
            break;
        case 2:
            strengthBar.classList.add('fair');
            strengthLabel = 'Fair';
            break;
        case 3:
        case 4:
            strengthBar.classList.add('good');
            strengthLabel = 'Good';
            break;
        case 5:
            strengthBar.classList.add('strong');
            strengthLabel = 'Strong';
            break;
    }

    strengthText.textContent = `Password strength: ${strengthLabel}`;
}

// Setup real-time validation
function setupRealTimeValidation() {
    const inputs = document.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => {
            if (input.classList.contains('error')) {
                validateField(input);
            }
        });
    });

    // Confirm password validation
    const confirmPassword = document.getElementById('confirmPassword');
    if (confirmPassword) {
        confirmPassword.addEventListener('input', validatePasswordMatch);
    }
}

// Validate individual field
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';

    // Clear previous error
    clearFieldError(field);

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required';
    }

    // Email validation
    if (fieldName === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address';
        }
    }

    // Password validation
    if (fieldName === 'password' && value) {
        if (value.length < 8) {
            isValid = false;
            errorMessage = 'Password must be at least 8 characters long';
        }
    }

    // Name validation
    if ((fieldName === 'firstName' || fieldName === 'lastName') && value) {
        if (value.length < 2) {
            isValid = false;
            errorMessage = 'Name must be at least 2 characters long';
        }
    }

    // Show error if invalid
    if (!isValid) {
        showFieldError(field, errorMessage);
    }

    return isValid;
}

// Validate password match
function validatePasswordMatch() {
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmField = document.getElementById('confirmPassword');

    clearFieldError(confirmField);

    if (confirmPassword && password !== confirmPassword) {
        showFieldError(confirmField, 'Passwords do not match');
        return false;
    }

    return true;
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('error');
    const errorElement = document.getElementById(`${field.id}Error`);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }
}

// Clear field error
function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = document.getElementById(`${field.id}Error`);
    if (errorElement) {
        errorElement.textContent = '';
        errorElement.classList.remove('show');
    }
}

// Clear all errors
function clearAllErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    const inputElements = document.querySelectorAll('input.error');
    
    errorElements.forEach(el => {
        el.textContent = '';
        el.classList.remove('show');
    });
    
    inputElements.forEach(el => {
        el.classList.remove('error');
    });
}

// Handle login form submission
async function handleLogin(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const loginData = {
        email: formData.get('email'),
        password: formData.get('password')
    };

    // Validate form
    const emailField = document.getElementById('loginEmail');
    const passwordField = document.getElementById('loginPassword');
    
    let isValid = true;
    isValid = validateField(emailField) && isValid;
    isValid = validateField(passwordField) && isValid;

    if (!isValid) return;

    // Show loading state
    const submitBtn = e.target.querySelector('.auth-btn.primary');
    showButtonLoading(submitBtn, true);

    try {
        const response = await fetch(`${API_BASE_URL}/user/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        });

        const result = await response.json();

        if (response.ok && result.success) {
            // Store auth token
            localStorage.setItem('authToken', result.token);
            localStorage.setItem('user', JSON.stringify(result.user));
            
            showToast('Login successful! Welcome back.', 'success');
            
            // Redirect to intended page or home
            const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '../index.html';
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1500);
        } else {
            showToast(result.message || 'Login failed. Please check your credentials.', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showToast('Network error. Please try again.', 'error');
    } finally {
        showButtonLoading(submitBtn, false);
    }
}

// Handle register form submission
async function handleRegister(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const registerData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        password: formData.get('password')
    };

    // Validate form
    const fields = ['firstName', 'lastName', 'registerEmail', 'registerPassword', 'confirmPassword'];
    let isValid = true;
    
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            isValid = validateField(field) && isValid;
        }
    });

    // Validate password match
    isValid = validatePasswordMatch() && isValid;

    // Check terms agreement
    const agreeTerms = document.getElementById('agreeTerms');
    if (!agreeTerms.checked) {
        showToast('Please agree to the Terms of Service and Privacy Policy', 'error');
        isValid = false;
    }

    if (!isValid) return;

    // Show loading state
    const submitBtn = e.target.querySelector('.auth-btn.primary');
    showButtonLoading(submitBtn, true);

    try {
        const response = await fetch(`${API_BASE_URL}/user/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(registerData)
        });

        const result = await response.json();

        if (response.ok && result.success) {
            // Store auth token
            localStorage.setItem('authToken', result.token);
            localStorage.setItem('user', JSON.stringify(result.user));
            
            showToast('Account created successfully! Welcome to StepStyle.', 'success');
            
            // Redirect to home page
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1500);
        } else {
            showToast(result.message || 'Registration failed. Please try again.', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showToast('Network error. Please try again.', 'error');
    } finally {
        showButtonLoading(submitBtn, false);
    }
}

// Handle forgot password form submission
async function handleForgotPassword(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const email = formData.get('email');

    // Validate email
    const emailField = document.getElementById('forgotEmail');
    if (!validateField(emailField)) return;

    // Show loading state
    const submitBtn = e.target.querySelector('.auth-btn.primary');
    showButtonLoading(submitBtn, true);

    try {
        // Simulate API call (implement actual forgot password endpoint)
        await new Promise(resolve => setTimeout(resolve, 2000));

        showToast('Password reset link sent to your email!', 'success');

        // Go back to login form after success
        setTimeout(() => {
            hideForgotPasswordForm();
        }, 2000);
    } catch (error) {
        console.error('Forgot password error:', error);
        showToast('Failed to send reset email. Please try again.', 'error');
    } finally {
        showButtonLoading(submitBtn, false);
    }
}

// Handle Google OAuth
async function handleGoogleAuth() {
    try {
        showToast('Google authentication coming soon!', 'warning');
        // Implement Google OAuth here
        // This would typically involve:
        // 1. Initialize Google OAuth
        // 2. Handle the OAuth flow
        // 3. Send the token to your backend
        // 4. Store the returned JWT token
    } catch (error) {
        console.error('Google auth error:', error);
        showToast('Google authentication failed. Please try again.', 'error');
    }
}

// Show/hide button loading state
function showButtonLoading(button, isLoading) {
    const btnText = button.querySelector('.btn-text');
    const btnLoader = button.querySelector('.btn-loader');

    if (isLoading) {
        btnText.style.opacity = '0';
        btnLoader.style.display = 'block';
        button.disabled = true;
    } else {
        btnText.style.opacity = '1';
        btnLoader.style.display = 'none';
        button.disabled = false;
    }
}

// Check authentication status
function checkAuthStatus() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');

    if (token && user) {
        // User is already logged in, redirect to home
        const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '../index.html';
        window.location.href = redirectUrl;
    }
}

// Show toast notification
function showToast(message, type = 'success') {
    const toast = document.querySelector('.toast-notification');
    const toastIcon = toast.querySelector('.toast-icon');
    const toastMessage = toast.querySelector('.toast-message');

    // Set message
    toastMessage.textContent = message;

    // Set type and icon
    toast.className = `toast-notification ${type}`;

    switch (type) {
        case 'success':
            toastIcon.className = 'toast-icon ri-check-line';
            break;
        case 'error':
            toastIcon.className = 'toast-icon ri-close-line';
            break;
        case 'warning':
            toastIcon.className = 'toast-icon ri-alert-line';
            break;
        default:
            toastIcon.className = 'toast-icon ri-information-line';
    }

    // Show toast
    toast.classList.add('show');

    // Hide after 4 seconds
    setTimeout(() => {
        toast.classList.remove('show');
    }, 4000);
}

// Utility function to get auth token
export function getAuthToken() {
    return localStorage.getItem('authToken');
}

// Utility function to get user data
export function getUserData() {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
}

// Utility function to logout
export function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    window.location.href = './login.html';
}

// Utility function to check if user is authenticated
export function isAuthenticated() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');
    return !!(token && user);
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - StepStyle</title>
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/fill/style.css"
    />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="../src/styles/login.css" />
    <link rel="stylesheet" href="../src/css/search-results.css" />
</head>
<body>
    <main>
        <!-- Navigation -->
        <header>
            <nav>
                <div id="logo">
                    <a href="../index.html">
                        <img src="../assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle" loading="lazy" />
                    </a>
                </div>
                <div id="nav-middle">
                    <a href="../index.html">Home</a>
                    <a href="./men.html">Men</a>
                    <a href="./women.html">Women</a>
                    <a href="./kids.html">Kids</a>
                    <a href="./about.html">About</a>
                </div>
                <div id="nav-last">
                    <div id="search-bar">
                        <i class="ph ph-magnifying-glass search-icon"></i>
                        <input
                            type="search"
                            placeholder="Search"
                            name="search"
                            id="nav-search"
                        />
                    </div>
                    <div class="cart-icon-container">
                        <i class="ph ph-shopping-cart cart"></i>
                        <span class="cart-count">0</span>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Login Section -->
        <section class="auth-section">
            <div class="auth-container">
                <!-- Background Design Elements -->
                <div class="auth-bg-design">
                    <div class="bg-circle-1"></div>
                    <div class="bg-circle-2"></div>
                    <div class="bg-gradient"></div>
                </div>

                <!-- Auth Forms Container -->
                <div class="auth-forms-container">
                    <!-- Tab Navigation -->
                    <div class="auth-tabs">
                        <button class="auth-tab active" data-tab="login">Sign In</button>
                        <button class="auth-tab" data-tab="register">Sign Up</button>
                    </div>

                    <!-- Login Form -->
                    <div class="auth-form-wrapper active" id="login-form">
                        <div class="auth-form">
                            <div class="form-header">
                                <h2>Welcome Back</h2>
                                <p>Sign in to your StepStyle account</p>
                            </div>

                            <form id="loginForm" class="form">
                                <div class="form-group">
                                    <label for="loginEmail">Email Address</label>
                                    <div class="input-wrapper">
                                        <i class="ri-mail-line input-icon"></i>
                                        <input type="email" id="loginEmail" name="email" required>
                                    </div>
                                    <span class="error-message" id="loginEmailError"></span>
                                </div>

                                <div class="form-group">
                                    <label for="loginPassword">Password</label>
                                    <div class="input-wrapper">
                                        <i class="ri-lock-line input-icon"></i>
                                        <input type="password" id="loginPassword" name="password" required>
                                        <button type="button" class="password-toggle" data-target="loginPassword">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                    <span class="error-message" id="loginPasswordError"></span>
                                </div>

                                <div class="form-options">
                                    <label class="checkbox-wrapper">
                                        <input type="checkbox" id="rememberMe">
                                        <span class="checkmark"></span>
                                        Remember me
                                    </label>
                                    <a href="#" class="forgot-password" id="forgotPasswordLink">Forgot Password?</a>
                                </div>

                                <button type="submit" class="auth-btn primary">
                                    <span class="btn-text">Sign In</span>
                                    <div class="btn-loader" style="display: none;">
                                        <div class="spinner"></div>
                                    </div>
                                </button>

                                <div class="divider">
                                    <span>or</span>
                                </div>

                                <button type="button" class="auth-btn google" id="googleLoginBtn">
                                    <i class="ri-google-fill"></i>
                                    Continue with Google
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Register Form -->
                    <div class="auth-form-wrapper" id="register-form">
                        <div class="auth-form">
                            <div class="form-header">
                                <h2>Create Account</h2>
                                <p>Join StepStyle and discover your perfect shoes</p>
                            </div>

                            <form id="registerForm" class="form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="firstName">First Name</label>
                                        <div class="input-wrapper">
                                            <i class="ri-user-line input-icon"></i>
                                            <input type="text" id="firstName" name="firstName" required>
                                        </div>
                                        <span class="error-message" id="firstNameError"></span>
                                    </div>

                                    <div class="form-group">
                                        <label for="lastName">Last Name</label>
                                        <div class="input-wrapper">
                                            <i class="ri-user-line input-icon"></i>
                                            <input type="text" id="lastName" name="lastName" required>
                                        </div>
                                        <span class="error-message" id="lastNameError"></span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="registerEmail">Email Address</label>
                                    <div class="input-wrapper">
                                        <i class="ri-mail-line input-icon"></i>
                                        <input type="email" id="registerEmail" name="email" required>
                                    </div>
                                    <span class="error-message" id="registerEmailError"></span>
                                </div>

                                <div class="form-group">
                                    <label for="registerPassword">Password</label>
                                    <div class="input-wrapper">
                                        <i class="ri-lock-line input-icon"></i>
                                        <input type="password" id="registerPassword" name="password" required>
                                        <button type="button" class="password-toggle" data-target="registerPassword">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                    <span class="error-message" id="registerPasswordError"></span>
                                    <div class="password-strength">
                                        <div class="strength-bar">
                                            <div class="strength-fill"></div>
                                        </div>
                                        <span class="strength-text">Password strength</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="confirmPassword">Confirm Password</label>
                                    <div class="input-wrapper">
                                        <i class="ri-lock-line input-icon"></i>
                                        <input type="password" id="confirmPassword" name="confirmPassword" required>
                                        <button type="button" class="password-toggle" data-target="confirmPassword">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                    <span class="error-message" id="confirmPasswordError"></span>
                                </div>

                                <div class="form-options">
                                    <label class="checkbox-wrapper">
                                        <input type="checkbox" id="agreeTerms" required>
                                        <span class="checkmark"></span>
                                        I agree to the <a href="#" class="terms-link">Terms of Service</a> and <a href="#" class="terms-link">Privacy Policy</a>
                                    </label>
                                </div>

                                <button type="submit" class="auth-btn primary">
                                    <span class="btn-text">Create Account</span>
                                    <div class="btn-loader" style="display: none;">
                                        <div class="spinner"></div>
                                    </div>
                                </button>

                                <div class="divider">
                                    <span>or</span>
                                </div>

                                <button type="button" class="auth-btn google" id="googleRegisterBtn">
                                    <i class="ri-google-fill"></i>
                                    Continue with Google
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Forgot Password Form -->
                    <div class="auth-form-wrapper" id="forgot-password-form">
                        <div class="auth-form">
                            <div class="form-header">
                                <h2>Reset Password</h2>
                                <p>Enter your email address and we'll send you a link to reset your password</p>
                            </div>

                            <form id="forgotPasswordForm" class="form">
                                <div class="form-group">
                                    <label for="forgotEmail">Email Address</label>
                                    <div class="input-wrapper">
                                        <i class="ri-mail-line input-icon"></i>
                                        <input type="email" id="forgotEmail" name="email" required>
                                    </div>
                                    <span class="error-message" id="forgotEmailError"></span>
                                </div>

                                <button type="submit" class="auth-btn primary">
                                    <span class="btn-text">Send Reset Link</span>
                                    <div class="btn-loader" style="display: none;">
                                        <div class="spinner"></div>
                                    </div>
                                </button>

                                <button type="button" class="auth-btn secondary back-to-login">
                                    <i class="ri-arrow-left-line"></i>
                                    Back to Sign In
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Brand Showcase -->
                <div class="brand-showcase">
                    <div class="showcase-content">
                        <div class="brand-logo">
                            <img src="../assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle">
                        </div>
                        <h3>Step Into Your Style</h3>
                        <p>Discover the perfect shoes for every step of your journey. From casual comfort to athletic performance, find your ideal pair at StepStyle.</p>
                        <div class="showcase-features">
                            <div class="feature">
                                <i class="ri-shield-check-line"></i>
                                <span>Secure Shopping</span>
                            </div>
                            <div class="feature">
                                <i class="ri-truck-line"></i>
                                <span>Free Shipping</span>
                            </div>
                            <div class="feature">
                                <i class="ri-refresh-line"></i>
                                <span>Easy Returns</span>
                            </div>
                        </div>
                    </div>
                    <div class="showcase-image">
                        <img src="../assets/images/nike-airForce.png" alt="Featured Shoe">
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Cart Modal (same as other pages) -->
    <div class="cart-overlay"></div>
    <div class="cart-modal">
        <div class="cart-modal-content">
            <div class="cart-header">
                <h3>Your Cart</h3>
                <button class="close-cart-btn">&times;</button>
            </div>
            <div class="cart-items">
                <!-- Cart items will be dynamically added here -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <span>Total:</span>
                    <span class="total-amount">₹0</span>
                </div>
                <button class="checkout-btn">Checkout</button>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast-notification">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <div class="toast-message"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="../src/js/search.js"></script>
    <script type="module" src="../src/js/cart.js"></script>
    <script type="module" src="../src/js/login.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - StepStyle</title>
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/fill/style.css"
    />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="../src/styles/login.css">
</head>
<body>
    <div class="login-container">
        <!-- Left Side - Dark Section with Animated Shoe -->
        <div class="left-section">
            <div class="brand-header">
                <h1>StepStyle</h1>
            </div>

            <div class="shoe-container">
                <img src="https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/dbd2620b-a99f-4279-97db-0344edf84e31/NIKE+DUNK+LOW+RETRO.png"
                     alt="Nike Shoe" class="animated-shoe" id="animatedShoe">
            </div>

            <div class="brand-footer">
                <div class="brand-info">
                    <h3>StepStyle</h3>
                    <p>Footwear & Fashion</p>
                </div>
                <div class="social-icons">
                    <button class="social-btn"><i class="ph ph-heart"></i></button>
                    <button class="social-btn"><i class="ph ph-share-network"></i></button>
                </div>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="right-section">
            <div class="language-selector">
                <span>EN</span>
                <i class="ph ph-caret-down"></i>
            </div>

            <div class="form-header">
                <h1>STEPSTYLE</h1>
                <h2>Welcome Back</h2>
                <p>Sign in to continue your shopping experience</p>
            </div>

            <!-- Form Container -->
            <div class="form-container">
                <!-- Login Form -->
                <form id="loginForm" class="auth-form active">
                    <div class="form-group">
                        <label for="loginEmail">Email</label>
                        <input type="email" id="loginEmail" name="email" placeholder="<EMAIL>" required>
                    </div>

                    <div class="form-group password-field">
                        <label for="loginPassword">Password</label>
                        <div class="password-input-container">
                            <input type="password" id="loginPassword" name="password" placeholder="••••••••••" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">
                                <i class="ph ph-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="forgot-link">
                        <a href="#" onclick="showForgotPassword()">Forgot password?</a>
                    </div>

                    <button type="button" class="google-btn">
                        <i class="ri-google-fill"></i>
                        Login with Google
                    </button>

                    <button type="submit" class="login-btn">Login</button>

                    <div class="signup-link">
                        Don't have an account? <a href="#" onclick="switchToSignup()">Sign up</a>
                    </div>
                </form>

                <!-- Signup Form -->
                <form id="signupForm" class="auth-form">
                    <div class="form-group">
                        <label for="signupName">Full Name</label>
                        <input type="text" id="signupName" name="name" placeholder="Enter your full name" required>
                    </div>

                    <div class="form-group">
                        <label for="signupEmail">Email</label>
                        <input type="email" id="signupEmail" name="email" placeholder="Enter your email" required>
                    </div>

                    <div class="form-group password-field">
                        <label for="signupPassword">Password</label>
                        <div class="password-input-container">
                            <input type="password" id="signupPassword" name="password" placeholder="••••••••••" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('signupPassword')">
                                <i class="ph ph-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group password-field">
                        <label for="confirmPassword">Confirm Password</label>
                        <div class="password-input-container">
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="••••••••••" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                <i class="ph ph-eye"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="login-btn">Sign Up</button>

                    <div class="signup-link">
                        Already have an account? <a href="#" onclick="switchToLogin()">Sign in</a>
                    </div>
                </form>

                <!-- Forgot Password Form -->
                <form id="forgotPasswordForm" class="auth-form">
                    <div class="form-group">
                        <label for="forgotEmail">Email</label>
                        <input type="email" id="forgotEmail" name="email" placeholder="Enter your email" required>
                    </div>

                    <button type="submit" class="login-btn">Send Reset Link</button>

                    <div class="signup-link">
                        Remember your password? <a href="#" onclick="switchToLogin()">Sign in</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
            <p>Please wait...</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Scripts -->
    <script src="../src/js/auth.js"></script>
    <script src="../src/js/auth-ui.js"></script>
</body>
</html>

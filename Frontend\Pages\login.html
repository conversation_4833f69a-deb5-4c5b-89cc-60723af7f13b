<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - StepStyle</title>
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/fill/style.css"
    />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="../src/styles/login.css">
    
    <!-- Google OAuth -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <div class="login-container">
        <!-- Background Elements -->
        <div class="background-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>

        <!-- Login Card -->
        <div class="login-card">
            <!-- Logo Section -->
            <div class="logo-section">
                <img src="../assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle" class="logo">
                <h1>Welcome to StepStyle</h1>
                <p class="subtitle">Step into your style journey</p>
            </div>

            <!-- Form Container -->
            <div class="form-container">
                <!-- Login Form -->
                <form id="loginForm" class="auth-form active">
                    <h2>Sign In</h2>
                    <div class="form-group">
                        <label for="loginEmail">Email</label>
                        <input type="email" id="loginEmail" name="email" required>
                        <i class="ph ph-envelope input-icon"></i>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">Password</label>
                        <input type="password" id="loginPassword" name="password" required>
                        <i class="ph ph-lock input-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">
                            <i class="ph ph-eye"></i>
                        </button>
                    </div>
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#" class="forgot-password" onclick="showForgotPassword()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="auth-btn primary">Sign In</button>
                    
                    <!-- Google OAuth Button -->
                    <div class="divider">
                        <span>or</span>
                    </div>
                    <button type="button" class="auth-btn google" id="googleSignIn">
                        <i class="ri-google-fill"></i>
                        Sign in with Google
                    </button>
                    
                    <p class="switch-form">
                        Don't have an account? 
                        <a href="#" onclick="switchToSignup()">Sign up</a>
                    </p>
                </form>

                <!-- Signup Form -->
                <form id="signupForm" class="auth-form">
                    <h2>Sign Up</h2>
                    <div class="form-group">
                        <label for="signupName">Full Name</label>
                        <input type="text" id="signupName" name="name" required>
                        <i class="ph ph-user input-icon"></i>
                    </div>
                    <div class="form-group">
                        <label for="signupEmail">Email</label>
                        <input type="email" id="signupEmail" name="email" required>
                        <i class="ph ph-envelope input-icon"></i>
                    </div>
                    <div class="form-group">
                        <label for="signupPassword">Password</label>
                        <input type="password" id="signupPassword" name="password" required>
                        <i class="ph ph-lock input-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('signupPassword')">
                            <i class="ph ph-eye"></i>
                        </button>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required>
                        <i class="ph ph-lock input-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                            <i class="ph ph-eye"></i>
                        </button>
                    </div>
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="agreeTerms" required>
                            <span class="checkmark"></span>
                            I agree to the <a href="#" class="terms-link">Terms & Conditions</a>
                        </label>
                    </div>
                    <button type="submit" class="auth-btn primary">Sign Up</button>
                    
                    <!-- Google OAuth Button -->
                    <div class="divider">
                        <span>or</span>
                    </div>
                    <button type="button" class="auth-btn google" id="googleSignUp">
                        <i class="ri-google-fill"></i>
                        Sign up with Google
                    </button>
                    
                    <p class="switch-form">
                        Already have an account? 
                        <a href="#" onclick="switchToLogin()">Sign in</a>
                    </p>
                </form>

                <!-- Forgot Password Form -->
                <form id="forgotPasswordForm" class="auth-form">
                    <h2>Reset Password</h2>
                    <p class="form-description">Enter your email address and we'll send you a link to reset your password.</p>
                    <div class="form-group">
                        <label for="forgotEmail">Email</label>
                        <input type="email" id="forgotEmail" name="email" required>
                        <i class="ph ph-envelope input-icon"></i>
                    </div>
                    <button type="submit" class="auth-btn primary">Send Reset Link</button>
                    <p class="switch-form">
                        Remember your password? 
                        <a href="#" onclick="switchToLogin()">Sign in</a>
                    </p>
                </form>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
            <p>Please wait...</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Scripts -->
    <script src="../src/js/auth.js"></script>
    <script src="../src/js/auth-ui.js"></script>
</body>
</html>
